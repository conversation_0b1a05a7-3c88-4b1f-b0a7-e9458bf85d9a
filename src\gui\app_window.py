"""
Main application window for TikTok Automation
"""

import os
import logging
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from typing import Dict, Any, Optional, List, Callable
import threading
import time
from datetime import datetime
import webbrowser

from src.database.db_manager import DatabaseManager
from src.gui.account_frame import Account<PERSON>rame
from src.gui.video_frame import VideoFrame
from src.gui.settings_frame import SettingsFrame
from src.gui.log_frame import LogFrame

class AppWindow:
    """Main application window for TikTok Automation"""

    def __init__(self, root: tk.Tk, db_manager: DatabaseManager, config: Dict[str, Any]):
        """Initialize the main application window

        Args:
            root: Tkinter root window
            db_manager: Database manager instance
            config: Application configuration
        """
        self.root = root
        self.db_manager = db_manager
        self.config = config

        # Set window properties
        self.root.title(f"{config['app']['name']} v{config['app']['version']}")
        self.root.geometry("1200x800")
        self.root.minsize(800, 600)

        # Set icon if available
        icon_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))),
                                "assets", "icon.ico")
        if os.path.exists(icon_path):
            self.root.iconbitmap(icon_path)

        # Create main frame
        self.main_frame = ttk.Frame(self.root)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Create status bar (moved before creating tabs)
        self.status_var = tk.StringVar()
        self.status_var.set("Ready")
        self.status_bar = ttk.Label(self.root, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)

        # Create notebook for tabs
        self.notebook = ttk.Notebook(self.main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)

        # Initialize worker thread
        self.worker_thread = None
        self.stop_event = threading.Event()

        # Bind close event
        self.root.protocol("WM_DELETE_WINDOW", self.on_close)

        # Set initial status
        self.set_status("Application started")

        # Create tabs (moved after status_var is initialized)
        self.create_tabs()

    def create_tabs(self):
        """Create application tabs"""
        # Accounts tab
        self.accounts_frame = AccountFrame(self.notebook, self.db_manager, self.config, self.set_status)
        self.notebook.add(self.accounts_frame, text="Accounts")

        # Videos tab
        self.videos_frame = VideoFrame(self.notebook, self.db_manager, self.config, self.set_status)
        self.notebook.add(self.videos_frame, text="Videos")

        # Settings tab
        self.settings_frame = SettingsFrame(self.notebook, self.config, self.set_status)
        self.notebook.add(self.settings_frame, text="Settings")

        # Logs tab
        self.logs_frame = LogFrame(self.notebook)
        self.notebook.add(self.logs_frame, text="Logs")

        # Bind tab change event
        self.notebook.bind("<<NotebookTabChanged>>", self.on_tab_changed)

    def on_tab_changed(self, event):
        """Handle tab change event

        Args:
            event: Tab change event
        """
        tab_id = self.notebook.select()
        tab_name = self.notebook.tab(tab_id, "text")

        # Refresh data when switching to certain tabs
        if tab_name == "Accounts":
            self.accounts_frame.refresh_accounts()
        elif tab_name == "Videos":
            self.videos_frame.refresh_videos()
        elif tab_name == "Logs":
            self.logs_frame.refresh_logs()

    def set_status(self, message: str):
        """Set status bar message

        Args:
            message: Status message
        """
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.status_var.set(f"[{timestamp}] {message}")
        logging.info(message)

        # Also update log frame if it exists
        if hasattr(self, 'logs_frame'):
            self.logs_frame.add_log_entry(f"[{timestamp}] {message}")

    def run_in_thread(self, func: Callable, *args, **kwargs):
        """Run a function in a separate thread

        Args:
            func: Function to run
            *args: Function arguments
            **kwargs: Function keyword arguments
        """
        if self.worker_thread and self.worker_thread.is_alive():
            messagebox.showwarning("Warning", "A task is already running. Please wait for it to complete.")
            return

        # Reset stop event
        self.stop_event.clear()

        # Create and start thread
        self.worker_thread = threading.Thread(target=self._thread_wrapper, args=(func, args, kwargs))
        self.worker_thread.daemon = True
        self.worker_thread.start()

    def _thread_wrapper(self, func: Callable, args, kwargs):
        """Wrapper for thread execution with error handling

        Args:
            func: Function to run
            args: Function arguments
            kwargs: Function keyword arguments
        """
        try:
            self.set_status(f"Starting task: {func.__name__}")
            result = func(*args, **kwargs)
            if not self.stop_event.is_set():
                self.set_status(f"Task completed: {func.__name__}")
            return result
        except Exception as e:
            error_message = f"Error in {func.__name__}: {str(e)}"
            logging.error(error_message, exc_info=True)
            self.set_status(error_message)

            # Show error message in main thread
            self.root.after(0, lambda: messagebox.showerror("Error", error_message))

    def stop_current_task(self):
        """Stop the currently running task"""
        if self.worker_thread and self.worker_thread.is_alive():
            self.stop_event.set()
            self.set_status("Stopping current task...")
        else:
            self.set_status("No task is currently running")

    def on_close(self):
        """Handle application close event"""
        # Stop any running tasks
        self.stop_current_task()

        # Wait for thread to finish (with timeout)
        if self.worker_thread and self.worker_thread.is_alive():
            self.worker_thread.join(timeout=1.0)

        # Save any pending changes
        if hasattr(self, 'settings_frame'):
            self.settings_frame.save_settings()

        # Close the application
        self.root.destroy()
        logging.info("Application closed")
