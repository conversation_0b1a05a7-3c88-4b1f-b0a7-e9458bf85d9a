"""
Story Generator for TikTok Automation
Generates short stories based on account niche and duration
"""

import os
import logging
import time
from typing import Dict, Any, Optional, List
import json

# For OpenAI API
try:
    import openai
except ImportError:
    logging.warning("OpenAI package not installed. OpenAI story generation will not be available.")

# For Hugging Face Transformers
try:
    from transformers import pipeline
except ImportError:
    logging.warning("Transformers package not installed. Hugging Face story generation will not be available.")

class StoryGenerator:
    """Generates short stories for TikTok videos"""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize story generator
        
        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.provider = config['ai']['story_generation']['provider']
        self.model = config['ai']['story_generation']['model']
        self.temperature = config['ai']['story_generation']['temperature']
        self.max_tokens = config['ai']['story_generation']['max_tokens']
        
        # Initialize provider-specific resources
        if self.provider == 'openai':
            self._init_openai()
        elif self.provider == 'huggingface':
            self._init_huggingface()
        elif self.provider == 'local':
            self._init_local()
        else:
            raise ValueError(f"Unsupported story generation provider: {self.provider}")
    
    def _init_openai(self):
        """Initialize OpenAI API"""
        api_key = self.config['api_keys'].get('openai', os.environ.get('OPENAI_API_KEY'))
        if not api_key:
            raise ValueError("OpenAI API key not found in config or environment variables")
        
        openai.api_key = api_key
        logging.info("Initialized OpenAI story generator")
    
    def _init_huggingface(self):
        """Initialize Hugging Face Transformers"""
        self.generator = pipeline('text-generation', model=self.model)
        logging.info(f"Initialized Hugging Face story generator with model {self.model}")
    
    def _init_local(self):
        """Initialize local LLM"""
        # Implementation depends on the specific local model being used
        logging.info("Initialized local story generator")
        logging.warning("Local story generation not fully implemented")
    
    def generate_story(self, niche: str, duration_seconds: int, 
                       additional_context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Generate a short story based on niche and duration
        
        Args:
            niche: Content niche (e.g., "Scary Stories", "Motivation")
            duration_seconds: Target duration in seconds
            additional_context: Additional context for story generation
            
        Returns:
            Dict containing the generated story and metadata
        """
        logging.info(f"Generating story for niche: {niche}, duration: {duration_seconds}s")
        
        # Calculate approximate word count based on duration
        # Assuming average speaking rate of 150 words per minute
        target_word_count = int((duration_seconds / 60) * 150)
        
        # Generate prompt based on niche and target length
        prompt = self._create_prompt(niche, target_word_count, additional_context)
        
        # Generate story using selected provider
        if self.provider == 'openai':
            story_text = self._generate_with_openai(prompt)
        elif self.provider == 'huggingface':
            story_text = self._generate_with_huggingface(prompt)
        elif self.provider == 'local':
            story_text = self._generate_with_local(prompt)
        else:
            raise ValueError(f"Unsupported story generation provider: {self.provider}")
        
        # Extract title from the story or generate one
        title = self._extract_title(story_text)
        
        # Calculate estimated duration
        word_count = len(story_text.split())
        estimated_duration = int((word_count / 150) * 60)
        
        result = {
            'title': title,
            'text': story_text,
            'niche': niche,
            'word_count': word_count,
            'estimated_duration': estimated_duration,
            'target_duration': duration_seconds,
            'timestamp': time.time()
        }
        
        logging.info(f"Generated story: {title} ({word_count} words, ~{estimated_duration}s)")
        return result
    
    def _create_prompt(self, niche: str, target_word_count: int, 
                       additional_context: Optional[Dict[str, Any]] = None) -> str:
        """Create a prompt for story generation
        
        Args:
            niche: Content niche
            target_word_count: Target word count
            additional_context: Additional context for story generation
            
        Returns:
            str: Prompt for the AI model
        """
        # Base prompt template
        prompt = f"""Generate a short, engaging TikTok story for the "{niche}" niche.
The story should be approximately {target_word_count} words long (to fit in a {int(target_word_count/150*60)}-second video).
Make it captivating, with a clear beginning, middle, and end.
The story should grab attention in the first few seconds.
Use simple, conversational language that works well for narration.
"""
        
        # Add niche-specific instructions
        if "scary" in niche.lower() or "horror" in niche.lower():
            prompt += "Create a suspenseful, creepy story with a twist ending. Build tension throughout."
        elif "motivation" in niche.lower() or "inspire" in niche.lower():
            prompt += "Create an inspiring story with a powerful message that motivates the viewer."
        elif "comedy" in niche.lower() or "funny" in niche.lower():
            prompt += "Create a humorous story with a surprising punchline."
        elif "fact" in niche.lower() or "educational" in niche.lower():
            prompt += "Share an interesting, lesser-known fact and explain its significance in an engaging way."
        
        # Add additional context if provided
        if additional_context:
            if 'theme' in additional_context:
                prompt += f"\nThe theme should be related to: {additional_context['theme']}"
            if 'style' in additional_context:
                prompt += f"\nThe writing style should be: {additional_context['style']}"
            if 'audience' in additional_context:
                prompt += f"\nThe target audience is: {additional_context['audience']}"
        
        # Format instructions
        prompt += """
Format the response as follows:
Title: [Story Title]

[Story text here]
"""
        
        return prompt
    
    def _generate_with_openai(self, prompt: str) -> str:
        """Generate story using OpenAI API
        
        Args:
            prompt: Prompt for story generation
            
        Returns:
            str: Generated story text
        """
        try:
            response = openai.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "You are a creative storyteller for TikTok videos."},
                    {"role": "user", "content": prompt}
                ],
                temperature=self.temperature,
                max_tokens=self.max_tokens
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            logging.error(f"Error generating story with OpenAI: {str(e)}")
            raise
    
    def _generate_with_huggingface(self, prompt: str) -> str:
        """Generate story using Hugging Face Transformers
        
        Args:
            prompt: Prompt for story generation
            
        Returns:
            str: Generated story text
        """
        try:
            result = self.generator(prompt, max_length=self.max_tokens, 
                                   temperature=self.temperature, 
                                   num_return_sequences=1)
            # Extract the generated text from the result
            generated_text = result[0]['generated_text']
            # Remove the prompt from the generated text
            story_text = generated_text[len(prompt):].strip()
            return story_text
        except Exception as e:
            logging.error(f"Error generating story with Hugging Face: {str(e)}")
            raise
    
    def _generate_with_local(self, prompt: str) -> str:
        """Generate story using local LLM
        
        Args:
            prompt: Prompt for story generation
            
        Returns:
            str: Generated story text
        """
        # Implementation depends on the specific local model being used
        logging.warning("Local story generation not fully implemented")
        return f"Sample story for prompt: {prompt[:50]}..."
    
    def _extract_title(self, story_text: str) -> str:
        """Extract title from generated story
        
        Args:
            story_text: Generated story text
            
        Returns:
            str: Extracted title
        """
        # Look for "Title:" pattern
        if "Title:" in story_text:
            lines = story_text.split('\n')
            for line in lines:
                if line.strip().startswith("Title:"):
                    return line.replace("Title:", "").strip()
        
        # If no title found, extract first line or generate one
        lines = [line for line in story_text.split('\n') if line.strip()]
        if lines:
            # Use first line if it's short enough
            if len(lines[0]) < 50:
                return lines[0].strip()
            # Otherwise use first 5 words
            words = lines[0].split()
            return ' '.join(words[:5]) + "..."
        
        # Fallback
        return "Untitled Story"
