"""
TikTok Automation Application
Main entry point for the application
"""

import os
import sys
import logging
from datetime import datetime
from tkinter import Tk

# Import application modules
from src.gui.app_window import AppWindow
from src.utils.config import load_config, create_default_config
from src.utils.logger import setup_logger

def main():
    """Main application entry point"""
    # Setup logging
    log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs")
    os.makedirs(log_dir, exist_ok=True)
    log_file = os.path.join(log_dir, f"tiktok_automation_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    setup_logger(log_file)
    
    logging.info("Starting TikTok Automation Application")
    
    # Ensure config exists
    config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "config.json")
    if not os.path.exists(config_path):
        logging.info("Creating default configuration")
        create_default_config(config_path)
    
    # Load configuration
    config = load_config(config_path)
    
    # Initialize database
    from src.database.db_manager import DatabaseManager
    db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "data", "tiktok_automation.db")
    os.makedirs(os.path.dirname(db_path), exist_ok=True)
    db_manager = DatabaseManager(db_path)
    db_manager.initialize_database()
    
    # Start GUI
    root = Tk()
    app = AppWindow(root, db_manager, config)
    root.mainloop()
    
    logging.info("Application closed")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        logging.critical(f"Unhandled exception: {str(e)}", exc_info=True)
        sys.exit(1)
