"""
Log display frame for TikTok Automation
"""

import os
import tkinter as tk
from tkinter import ttk, scrolledtext, filedialog, messagebox
from typing import Dict, Any, Optional, List
import logging
import time
from datetime import datetime

class LogFrame(ttk.Frame):
    """Frame for displaying application logs"""
    
    def __init__(self, parent):
        """Initialize log frame
        
        Args:
            parent: Parent widget
        """
        super().__init__(parent)
        
        # Initialize log buffer
        self.log_buffer = []
        self.max_buffer_size = 1000  # Maximum number of log entries to keep in memory
        
        # Create UI elements
        self.create_widgets()
        
        # Load logs
        self.refresh_logs()
    
    def create_widgets(self):
        """Create UI widgets"""
        # Create main layout
        control_frame = ttk.Frame(self)
        control_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Log level filter
        ttk.Label(control_frame, text="Log Level:").pack(side=tk.LEFT, padx=5)
        self.log_level_var = tk.StringVar(value="INFO")
        level_combo = ttk.Combobox(control_frame, textvariable=self.log_level_var, width=10)
        level_combo['values'] = ("DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL")
        level_combo.pack(side=tk.LEFT, padx=5)
        level_combo.bind("<<ComboboxSelected>>", lambda e: self.filter_logs())
        
        # Search box
        ttk.Label(control_frame, text="Search:").pack(side=tk.LEFT, padx=5)
        self.search_var = tk.StringVar()
        search_entry = ttk.Entry(control_frame, textvariable=self.search_var, width=30)
        search_entry.pack(side=tk.LEFT, padx=5)
        search_entry.bind("<Return>", lambda e: self.filter_logs())
        
        # Search button
        ttk.Button(control_frame, text="Search", command=self.filter_logs).pack(side=tk.LEFT, padx=5)
        
        # Clear button
        ttk.Button(control_frame, text="Clear", command=self.clear_logs).pack(side=tk.LEFT, padx=5)
        
        # Refresh button
        ttk.Button(control_frame, text="Refresh", command=self.refresh_logs).pack(side=tk.RIGHT, padx=5)
        
        # Export button
        ttk.Button(control_frame, text="Export Logs", command=self.export_logs).pack(side=tk.RIGHT, padx=5)
        
        # Create log text area
        self.log_text = scrolledtext.ScrolledText(self, wrap=tk.WORD, height=25)
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Configure tags for different log levels
        self.log_text.tag_configure("DEBUG", foreground="gray")
        self.log_text.tag_configure("INFO", foreground="black")
        self.log_text.tag_configure("WARNING", foreground="orange")
        self.log_text.tag_configure("ERROR", foreground="red")
        self.log_text.tag_configure("CRITICAL", foreground="red", background="yellow")
        self.log_text.tag_configure("HIGHLIGHT", background="yellow")
    
    def refresh_logs(self):
        """Refresh logs from log files"""
        # Get log directory
        log_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "logs")
        
        if not os.path.exists(log_dir):
            self.log_text.delete(1.0, tk.END)
            self.log_text.insert(tk.END, "No log files found.")
            return
        
        # Get list of log files
        log_files = [f for f in os.listdir(log_dir) if f.endswith('.log')]
        
        if not log_files:
            self.log_text.delete(1.0, tk.END)
            self.log_text.insert(tk.END, "No log files found.")
            return
        
        # Sort by modification time (newest first)
        log_files.sort(key=lambda x: os.path.getmtime(os.path.join(log_dir, x)), reverse=True)
        
        # Read the most recent log file
        log_path = os.path.join(log_dir, log_files[0])
        
        try:
            with open(log_path, 'r') as f:
                log_lines = f.readlines()
            
            # Parse log lines
            self.log_buffer = []
            for line in log_lines:
                line = line.strip()
                if not line:
                    continue
                
                # Try to parse log level
                level = "INFO"  # Default level
                for lvl in ("DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"):
                    if f" - {lvl} - " in line:
                        level = lvl
                        break
                
                self.log_buffer.append((level, line))
            
            # Limit buffer size
            if len(self.log_buffer) > self.max_buffer_size:
                self.log_buffer = self.log_buffer[-self.max_buffer_size:]
            
            # Display logs
            self.filter_logs()
        except Exception as e:
            self.log_text.delete(1.0, tk.END)
            self.log_text.insert(tk.END, f"Error reading log file: {str(e)}")
    
    def filter_logs(self):
        """Filter logs based on level and search term"""
        # Get filter criteria
        min_level = self.log_level_var.get()
        search_term = self.search_var.get().lower()
        
        # Level priority
        level_priority = {
            "DEBUG": 0,
            "INFO": 1,
            "WARNING": 2,
            "ERROR": 3,
            "CRITICAL": 4
        }
        
        min_priority = level_priority.get(min_level, 0)
        
        # Clear text area
        self.log_text.delete(1.0, tk.END)
        
        # Filter and display logs
        for level, line in self.log_buffer:
            # Check level
            if level_priority.get(level, 0) < min_priority:
                continue
            
            # Check search term
            if search_term and search_term not in line.lower():
                continue
            
            # Add to text area with appropriate tag
            self.log_text.insert(tk.END, line + "\n", level)
            
            # Highlight search term if present
            if search_term:
                self.highlight_search_term(search_term)
        
        # Scroll to end
        self.log_text.see(tk.END)
    
    def highlight_search_term(self, search_term: str):
        """Highlight search term in log text
        
        Args:
            search_term: Term to highlight
        """
        start_pos = "1.0"
        while True:
            # Find next occurrence
            start_pos = self.log_text.search(search_term, start_pos, tk.END, nocase=True)
            if not start_pos:
                break
            
            # Calculate end position
            end_pos = f"{start_pos}+{len(search_term)}c"
            
            # Add highlight tag
            self.log_text.tag_add("HIGHLIGHT", start_pos, end_pos)
            
            # Move start position for next search
            start_pos = end_pos
    
    def clear_logs(self):
        """Clear log display"""
        self.log_text.delete(1.0, tk.END)
    
    def add_log_entry(self, message: str, level: str = "INFO"):
        """Add a new log entry
        
        Args:
            message: Log message
            level: Log level
        """
        # Add to buffer
        self.log_buffer.append((level, message))
        
        # Limit buffer size
        if len(self.log_buffer) > self.max_buffer_size:
            self.log_buffer.pop(0)
        
        # Check if entry should be displayed based on current filter
        min_level = self.log_level_var.get()
        search_term = self.search_var.get().lower()
        
        level_priority = {
            "DEBUG": 0,
            "INFO": 1,
            "WARNING": 2,
            "ERROR": 3,
            "CRITICAL": 4
        }
        
        min_priority = level_priority.get(min_level, 0)
        
        # Display if it passes filter
        if level_priority.get(level, 0) >= min_priority and (not search_term or search_term in message.lower()):
            self.log_text.insert(tk.END, message + "\n", level)
            
            # Highlight search term if present
            if search_term:
                self.highlight_search_term(search_term)
            
            # Scroll to end
            self.log_text.see(tk.END)
    
    def export_logs(self):
        """Export logs to a file"""
        # Get current timestamp for filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        default_filename = f"tiktok_automation_logs_{timestamp}.txt"
        
        # Ask for save location
        file_path = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")],
            initialfile=default_filename
        )
        
        if not file_path:
            return
        
        try:
            # Write logs to file
            with open(file_path, 'w') as f:
                for level, line in self.log_buffer:
                    f.write(line + "\n")
            
            messagebox.showinfo("Success", f"Logs exported to {file_path}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to export logs: {str(e)}")
