"""
Account management frame for TikTok Automation
"""

import tkinter as tk
from tkinter import ttk, messagebox
from typing import Dict, Any, Optional, List, Callable
import logging

from src.database.db_manager import DatabaseManager

class AccountFrame(ttk.Frame):
    """Frame for managing TikTok accounts"""
    
    def __init__(self, parent, db_manager: DatabaseManager, config: Dict[str, Any], 
                 status_callback: Callable[[str], None]):
        """Initialize account frame
        
        Args:
            parent: Parent widget
            db_manager: Database manager instance
            config: Application configuration
            status_callback: Callback function for status updates
        """
        super().__init__(parent)
        self.db_manager = db_manager
        self.config = config
        self.set_status = status_callback
        
        # Create UI elements
        self.create_widgets()
        
        # Load accounts
        self.refresh_accounts()
    
    def create_widgets(self):
        """Create UI widgets"""
        # Create main layout frames
        self.left_frame = ttk.Frame(self)
        self.left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.right_frame = ttk.Frame(self)
        self.right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, padx=5, pady=5)
        
        # Create account list
        self.create_account_list()
        
        # Create account form
        self.create_account_form()
        
        # Create buttons
        self.create_buttons()
    
    def create_account_list(self):
        """Create account list widget"""
        # Create frame with label
        list_frame = ttk.LabelFrame(self.left_frame, text="TikTok Accounts")
        list_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Create treeview with scrollbar
        columns = ("id", "username", "niche", "duration", "max_videos", "voice", "status")
        self.account_tree = ttk.Treeview(list_frame, columns=columns, show="headings")
        
        # Define column headings
        self.account_tree.heading("id", text="ID")
        self.account_tree.heading("username", text="Username")
        self.account_tree.heading("niche", text="Niche")
        self.account_tree.heading("duration", text="Duration (s)")
        self.account_tree.heading("max_videos", text="Max Videos/Day")
        self.account_tree.heading("voice", text="Voice Type")
        self.account_tree.heading("status", text="Status")
        
        # Define column widths
        self.account_tree.column("id", width=50)
        self.account_tree.column("username", width=150)
        self.account_tree.column("niche", width=150)
        self.account_tree.column("duration", width=100)
        self.account_tree.column("max_videos", width=120)
        self.account_tree.column("voice", width=100)
        self.account_tree.column("status", width=80)
        
        # Add scrollbar
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.account_tree.yview)
        self.account_tree.configure(yscrollcommand=scrollbar.set)
        
        # Pack widgets
        self.account_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Bind selection event
        self.account_tree.bind("<<TreeviewSelect>>", self.on_account_select)
    
    def create_account_form(self):
        """Create account form widgets"""
        form_frame = ttk.LabelFrame(self.right_frame, text="Account Details")
        form_frame.pack(fill=tk.BOTH, padx=5, pady=5)
        
        # Create form fields
        # Username
        ttk.Label(form_frame, text="Username:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.username_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.username_var, width=30).grid(row=0, column=1, padx=5, pady=5)
        
        # Label (optional)
        ttk.Label(form_frame, text="Label (optional):").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.label_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.label_var, width=30).grid(row=1, column=1, padx=5, pady=5)
        
        # Niche
        ttk.Label(form_frame, text="Niche:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        self.niche_var = tk.StringVar()
        niche_entry = ttk.Combobox(form_frame, textvariable=self.niche_var, width=27)
        niche_entry['values'] = ("Scary Stories", "Motivation", "Comedy", "Facts", "Educational", "Other")
        niche_entry.grid(row=2, column=1, padx=5, pady=5)
        
        # Video Duration
        ttk.Label(form_frame, text="Video Duration (s):").grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)
        self.duration_var = tk.IntVar(value=60)
        ttk.Spinbox(form_frame, from_=15, to=180, increment=15, textvariable=self.duration_var, width=28).grid(row=3, column=1, padx=5, pady=5)
        
        # Max Videos per Day
        ttk.Label(form_frame, text="Max Videos/Day:").grid(row=4, column=0, sticky=tk.W, padx=5, pady=5)
        self.max_videos_var = tk.IntVar(value=3)
        ttk.Spinbox(form_frame, from_=1, to=20, textvariable=self.max_videos_var, width=28).grid(row=4, column=1, padx=5, pady=5)
        
        # Voice Type
        ttk.Label(form_frame, text="Voice Type:").grid(row=5, column=0, sticky=tk.W, padx=5, pady=5)
        self.voice_var = tk.StringVar(value="female")
        voice_combo = ttk.Combobox(form_frame, textvariable=self.voice_var, width=27)
        voice_combo['values'] = ("male", "female", "ai")
        voice_combo.grid(row=5, column=1, padx=5, pady=5)
        
        # Status
        ttk.Label(form_frame, text="Status:").grid(row=6, column=0, sticky=tk.W, padx=5, pady=5)
        self.status_var = tk.StringVar(value="active")
        status_frame = ttk.Frame(form_frame)
        status_frame.grid(row=6, column=1, sticky=tk.W, padx=5, pady=5)
        ttk.Radiobutton(status_frame, text="Active", variable=self.status_var, value="active").pack(side=tk.LEFT)
        ttk.Radiobutton(status_frame, text="Inactive", variable=self.status_var, value="inactive").pack(side=tk.LEFT)
        
        # Hidden ID field for editing
        self.account_id_var = tk.IntVar(value=0)
    
    def create_buttons(self):
        """Create action buttons"""
        button_frame = ttk.Frame(self.right_frame)
        button_frame.pack(fill=tk.X, padx=5, pady=10)
        
        # Add/Save button
        self.save_button = ttk.Button(button_frame, text="Add Account", command=self.save_account)
        self.save_button.pack(side=tk.LEFT, padx=5)
        
        # Clear button
        ttk.Button(button_frame, text="Clear Form", command=self.clear_form).pack(side=tk.LEFT, padx=5)
        
        # Delete button
        self.delete_button = ttk.Button(button_frame, text="Delete", command=self.delete_account, state=tk.DISABLED)
        self.delete_button.pack(side=tk.LEFT, padx=5)
        
        # Refresh button
        ttk.Button(button_frame, text="Refresh", command=self.refresh_accounts).pack(side=tk.RIGHT, padx=5)
    
    def refresh_accounts(self):
        """Refresh account list from database"""
        # Clear existing items
        for item in self.account_tree.get_children():
            self.account_tree.delete(item)
        
        try:
            # Get accounts from database
            accounts = self.db_manager.get_all_accounts()
            
            # Add accounts to treeview
            for account in accounts:
                self.account_tree.insert("", tk.END, values=(
                    account['id'],
                    account['username'],
                    account['niche'],
                    account['video_duration'],
                    account['max_videos_per_day'],
                    account['voice_type'],
                    account['status']
                ))
            
            self.set_status(f"Loaded {len(accounts)} accounts")
        except Exception as e:
            self.set_status(f"Error loading accounts: {str(e)}")
            messagebox.showerror("Error", f"Failed to load accounts: {str(e)}")
    
    def on_account_select(self, event):
        """Handle account selection in treeview
        
        Args:
            event: Selection event
        """
        # Get selected item
        selected_items = self.account_tree.selection()
        if not selected_items:
            return
        
        # Get account data
        item = selected_items[0]
        account_data = self.account_tree.item(item, "values")
        
        # Populate form fields
        self.account_id_var.set(account_data[0])
        self.username_var.set(account_data[1])
        self.niche_var.set(account_data[2])
        self.duration_var.set(account_data[3])
        self.max_videos_var.set(account_data[4])
        self.voice_var.set(account_data[5])
        self.status_var.set(account_data[6])
        
        # Get label from database (not shown in treeview)
        try:
            account = self.db_manager.get_account(int(account_data[0]))
            if account:
                self.label_var.set(account.get('label', ''))
        except Exception as e:
            logging.error(f"Error getting account details: {str(e)}")
        
        # Update button states
        self.save_button.config(text="Save Changes")
        self.delete_button.config(state=tk.NORMAL)
    
    def clear_form(self):
        """Clear form fields and reset button states"""
        self.account_id_var.set(0)
        self.username_var.set("")
        self.label_var.set("")
        self.niche_var.set("")
        self.duration_var.set(60)
        self.max_videos_var.set(3)
        self.voice_var.set("female")
        self.status_var.set("active")
        
        # Reset button states
        self.save_button.config(text="Add Account")
        self.delete_button.config(state=tk.DISABLED)
        
        # Clear selection in treeview
        for item in self.account_tree.selection():
            self.account_tree.selection_remove(item)
    
    def save_account(self):
        """Save account (add new or update existing)"""
        # Validate form
        if not self.username_var.get().strip():
            messagebox.showerror("Error", "Username is required")
            return
        
        if not self.niche_var.get().strip():
            messagebox.showerror("Error", "Niche is required")
            return
        
        # Prepare account data
        account_data = {
            'username': self.username_var.get().strip(),
            'label': self.label_var.get().strip(),
            'niche': self.niche_var.get().strip(),
            'video_duration': self.duration_var.get(),
            'max_videos_per_day': self.max_videos_var.get(),
            'voice_type': self.voice_var.get(),
            'status': self.status_var.get()
        }
        
        try:
            # Check if adding or updating
            account_id = self.account_id_var.get()
            if account_id > 0:
                # Update existing account
                success = self.db_manager.update_account(account_id, account_data)
                if success:
                    self.set_status(f"Updated account: {account_data['username']}")
                    messagebox.showinfo("Success", f"Account '{account_data['username']}' updated successfully")
                else:
                    self.set_status(f"Failed to update account: {account_data['username']}")
                    messagebox.showerror("Error", "Failed to update account")
            else:
                # Add new account
                new_id = self.db_manager.add_account(account_data)
                self.set_status(f"Added new account: {account_data['username']} (ID: {new_id})")
                messagebox.showinfo("Success", f"Account '{account_data['username']}' added successfully")
            
            # Refresh account list
            self.refresh_accounts()
            
            # Clear form
            self.clear_form()
        except Exception as e:
            error_msg = f"Error saving account: {str(e)}"
            self.set_status(error_msg)
            messagebox.showerror("Error", error_msg)
    
    def delete_account(self):
        """Delete selected account"""
        account_id = self.account_id_var.get()
        if account_id <= 0:
            return
        
        # Confirm deletion
        if not messagebox.askyesno("Confirm Delete", 
                                  f"Are you sure you want to delete account '{self.username_var.get()}'?"):
            return
        
        try:
            # Delete account
            success = self.db_manager.delete_account(account_id)
            if success:
                self.set_status(f"Deleted account ID: {account_id}")
                messagebox.showinfo("Success", "Account deleted successfully")
                
                # Refresh account list
                self.refresh_accounts()
                
                # Clear form
                self.clear_form()
            else:
                self.set_status(f"Failed to delete account ID: {account_id}")
                messagebox.showerror("Error", "Failed to delete account")
        except Exception as e:
            error_msg = f"Error deleting account: {str(e)}"
            self.set_status(error_msg)
            messagebox.showerror("Error", error_msg)
