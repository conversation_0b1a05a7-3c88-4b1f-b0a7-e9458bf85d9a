"""
Configuration utilities for TikTok Automation
"""

import os
import json
import logging
from typing import Dict, Any

DEFAULT_CONFIG = {
    "app": {
        "name": "TikTok Automation",
        "version": "1.0.0"
    },
    "ai": {
        "story_generation": {
            "provider": "openai",  # openai, huggingface, local
            "model": "gpt-3.5-turbo",
            "temperature": 0.7,
            "max_tokens": 1000
        }
    },
    "tts": {
        "provider": "tts",  # tts, bark, other
        "voice_options": {
            "male": ["male1", "male2"],
            "female": ["female1", "female2"],
            "ai": ["ai1", "ai2"]
        }
    },
    "image": {
        "provider": "stable_diffusion",  # stable_diffusion, craiyon, other
        "resolution": {
            "width": 1080,
            "height": 1920
        }
    },
    "video": {
        "format": "mp4",
        "resolution": {
            "width": 1080,
            "height": 1920
        },
        "fps": 30,
        "effects": {
            "enabled": True,
            "default": "zoom"
        },
        "subtitles": {
            "enabled": True,
            "font": "Arial",
            "size": 40,
            "color": "white",
            "stroke_color": "black",
            "stroke_width": 2
        }
    },
    "export": {
        "output_dir": "output",
        "metadata": {
            "enabled": True,
            "format": "json"
        }
    },
    "api_keys": {
        "openai": "",
        "stable_diffusion": ""
    }
}

def create_default_config(config_path: str) -> None:
    """Create default configuration file
    
    Args:
        config_path: Path to save the configuration file
    """
    try:
        os.makedirs(os.path.dirname(config_path), exist_ok=True)
        with open(config_path, 'w') as f:
            json.dump(DEFAULT_CONFIG, f, indent=4)
        logging.info(f"Created default configuration at {config_path}")
    except Exception as e:
        logging.error(f"Error creating default configuration: {str(e)}")
        raise

def load_config(config_path: str) -> Dict[str, Any]:
    """Load configuration from file
    
    Args:
        config_path: Path to the configuration file
        
    Returns:
        Dict: Configuration dictionary
    """
    try:
        with open(config_path, 'r') as f:
            config = json.load(f)
        logging.info(f"Loaded configuration from {config_path}")
        return config
    except Exception as e:
        logging.error(f"Error loading configuration: {str(e)}")
        # Create and return default config if loading fails
        create_default_config(config_path)
        return DEFAULT_CONFIG

def save_config(config_path: str, config: Dict[str, Any]) -> None:
    """Save configuration to file
    
    Args:
        config_path: Path to save the configuration file
        config: Configuration dictionary to save
    """
    try:
        with open(config_path, 'w') as f:
            json.dump(config, f, indent=4)
        logging.info(f"Saved configuration to {config_path}")
    except Exception as e:
        logging.error(f"Error saving configuration: {str(e)}")
        raise

def update_config(config_path: str, updates: Dict[str, Any]) -> Dict[str, Any]:
    """Update configuration with new values
    
    Args:
        config_path: Path to the configuration file
        updates: Dictionary with updates to apply
        
    Returns:
        Dict: Updated configuration dictionary
    """
    config = load_config(config_path)
    
    def update_nested_dict(d, u):
        for k, v in u.items():
            if isinstance(v, dict) and k in d and isinstance(d[k], dict):
                update_nested_dict(d[k], v)
            else:
                d[k] = v
    
    update_nested_dict(config, updates)
    save_config(config_path, config)
    return config
