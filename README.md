# TikTok Automation

A Python application for automating the generation and management of TikTok reels/shorts for multiple TikTok accounts.

## Features

### Multi-account Management
- Store and manage multiple TikTok accounts
- Each account has its own profile with:
  - Username / label
  - Niche (e.g., "Scary Stories", "Motivation", etc.)
  - Video duration (e.g., 60s)
  - Max videos per day
  - Preferred voice type (e.g., female, male, AI style)
  - Status (active/inactive)

### Content Generation
- **Story Generation**: Automatically generate short stories based on the account niche using AI
- **Text-to-Speech**: Convert stories to speech using various voice types
- **Cover Generation**: Generate relevant images for video covers
- **Video Creation**: Combine audio, images, and effects into complete videos
- **Export**: Save videos and metadata for upload

## Installation

### Prerequisites
- Python 3.8 or higher
- Required Python packages (see `requirements.txt`)

### Option 1: Install from Source
1. Clone this repository:
   ```
   git clone https://github.com/yourusername/tiktok_automatisation.git
   cd tiktok_automatisation
   ```

2. Install dependencies:
   ```
   pip install -r requirements.txt
   ```

3. Run the application:
   ```
   python main.py
   ```

### Option 2: Use Pre-built Executable
1. Download the latest release from the Releases page
2. Extract the ZIP file
3. Run `TikTok_Automation.exe`

## Building the Executable

To build the executable yourself:

1. Install PyInstaller:
   ```
   pip install pyinstaller
   ```

2. Run the build script:
   ```
   python build_exe.py
   ```

3. Find the executable in the `dist` folder

## Configuration

The application uses a configuration file (`config.json`) to store settings. This file is created automatically on first run, or you can modify it through the Settings tab in the application.

### API Keys

To use all features, you'll need to provide API keys for:
- OpenAI (for story generation)
- Stable Diffusion (for image generation)

Enter these in the Settings > API Keys tab.

## Usage

### 1. Account Management
- Add TikTok accounts with their specific settings
- Set the niche, preferred video duration, and voice type

### 2. Video Generation
- Select an account
- Click "Generate Video" to create a new video
- The application will:
  1. Generate a story based on the account niche
  2. Convert the story to speech
  3. Generate a cover image
  4. Create a video with effects and subtitles

### 3. Export
- Select a generated video
- Click "Export Video" to save it for upload
- The video and metadata will be saved to the configured output directory

## Project Structure

```
tiktok_automatisation/
├── main.py                  # Main entry point
├── requirements.txt         # Dependencies
├── build_exe.py             # Executable build script
├── config.json              # Configuration file
├── src/                     # Source code
│   ├── database/            # Database operations
│   ├── story_generation/    # AI story generation
│   ├── tts/                 # Text-to-speech
│   ├── image_generation/    # Cover image generation
│   ├── video_generation/    # Video creation
│   ├── export/              # Export functionality
│   ├── gui/                 # User interface
│   └── utils/               # Utilities
├── data/                    # Generated data
│   ├── audio/               # Generated audio files
│   ├── images/              # Generated images
│   └── videos/              # Generated videos
├── logs/                    # Application logs
└── output/                  # Exported videos
```

## Dependencies

- **openai**: Story generation
- **TTS**: Text-to-speech
- **moviepy**: Video creation
- **Pillow**: Image processing
- **requests**: API communication
- **SQLite3**: Database storage
- **tkinter**: GUI framework
- **PyInstaller**: Executable creation

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Disclaimer

This tool is intended for content creators to streamline their workflow. Users are responsible for ensuring that generated content complies with TikTok's terms of service and community guidelines.
