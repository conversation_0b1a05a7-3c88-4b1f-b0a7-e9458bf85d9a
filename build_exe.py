"""
Build script for TikTok Automation executable
"""

import os
import sys
import shutil
import subprocess
import argparse
from pathlib import Path

def build_executable(clean=False, onefile=True, console=False):
    """Build executable using PyInstaller
    
    Args:
        clean: Whether to clean build files before building
        onefile: Whether to build a single executable file
        console: Whether to show console window
    """
    print("Building TikTok Automation executable...")
    
    # Get script directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Clean build files if requested
    if clean:
        print("Cleaning build files...")
        build_dir = os.path.join(script_dir, "build")
        dist_dir = os.path.join(script_dir, "dist")
        spec_file = os.path.join(script_dir, "tiktok_automation.spec")
        
        if os.path.exists(build_dir):
            shutil.rmtree(build_dir)
        
        if os.path.exists(dist_dir):
            shutil.rmtree(dist_dir)
        
        if os.path.exists(spec_file):
            os.remove(spec_file)
    
    # Create assets directory if it doesn't exist
    assets_dir = os.path.join(script_dir, "assets")
    os.makedirs(assets_dir, exist_ok=True)
    
    # Create icon file path
    icon_path = os.path.join(assets_dir, "icon.ico")
    
    # Check if icon exists, create a default one if not
    if not os.path.exists(icon_path):
        try:
            from PIL import Image, ImageDraw
            
            # Create a simple icon
            img = Image.new('RGB', (256, 256), color='purple')
            draw = ImageDraw.Draw(img)
            draw.ellipse((50, 50, 206, 206), fill='white')
            draw.rectangle((90, 90, 166, 166), fill='purple')
            
            # Save as .ico
            img.save(icon_path, format='ICO')
            print(f"Created default icon at {icon_path}")
        except Exception as e:
            print(f"Warning: Could not create default icon: {str(e)}")
            icon_path = None
    
    # Build command
    cmd = ["pyinstaller", "--name=TikTok_Automation"]
    
    if onefile:
        cmd.append("--onefile")
    else:
        cmd.append("--onedir")
    
    if not console:
        cmd.append("--noconsole")
    
    if icon_path and os.path.exists(icon_path):
        cmd.extend(["--icon", icon_path])
    
    # Add data files
    cmd.extend([
        "--add-data", f"{os.path.join(script_dir, 'assets')}:assets",
    ])
    
    # Add main script
    cmd.append(os.path.join(script_dir, "main.py"))
    
    # Run PyInstaller
    print("Running PyInstaller with command:")
    print(" ".join(cmd))
    
    result = subprocess.run(cmd, cwd=script_dir)
    
    if result.returncode == 0:
        print("\nBuild successful!")
        
        # Get output path
        if onefile:
            exe_path = os.path.join(script_dir, "dist", "TikTok_Automation.exe")
        else:
            exe_path = os.path.join(script_dir, "dist", "TikTok_Automation", "TikTok_Automation.exe")
        
        print(f"Executable created at: {exe_path}")
    else:
        print("\nBuild failed!")
        sys.exit(1)

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Build TikTok Automation executable")
    parser.add_argument("--clean", action="store_true", help="Clean build files before building")
    parser.add_argument("--onedir", dest="onefile", action="store_false", help="Build as directory instead of single file")
    parser.add_argument("--console", action="store_true", help="Show console window")
    
    args = parser.parse_args()
    
    build_executable(clean=args.clean, onefile=args.onefile, console=args.console)
