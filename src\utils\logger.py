"""
Logging configuration for TikTok Automation
"""

import logging
import sys
from logging.handlers import RotatingFile<PERSON><PERSON><PERSON>

def setup_logger(log_file=None, log_level=logging.INFO):
    """Configure logging for the application
    
    Args:
        log_file: Path to log file (optional)
        log_level: Logging level (default: INFO)
    """
    # Create logger
    logger = logging.getLogger()
    logger.setLevel(log_level)
    
    # Remove existing handlers
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # Create formatters
    file_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s')
    console_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    
    # Create console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(console_formatter)
    logger.addHandler(console_handler)
    
    # Create file handler if log file is specified
    if log_file:
        file_handler = RotatingFile<PERSON>andler(log_file, maxBytes=10*1024*1024, backupCount=5)
        file_handler.setFormatter(file_formatter)
        logger.addHandler(file_handler)
    
    logging.info("Logging configured")
    return logger
