"""
Export Manager for TikTok Automation
Handles exporting videos and metadata for upload
"""

import os
import logging
import time
import json
import shutil
from typing import Dict, Any, Optional, List
from datetime import datetime

class ExportManager:
    """Manages exporting videos and metadata for TikTok uploads"""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize export manager
        
        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.output_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 
                                      config['export']['output_dir'])
        self.metadata_enabled = config['export']['metadata']['enabled']
        self.metadata_format = config['export']['metadata']['format']
        
        # Create output directory structure
        os.makedirs(self.output_dir, exist_ok=True)
    
    def export_video(self, video_path: str, account_data: Dict[str, Any], 
                     video_data: Dict[str, Any]) -> Dict[str, Any]:
        """Export a video for upload
        
        Args:
            video_path: Path to the video file
            account_data: Account data dictionary
            video_data: Video data dictionary
            
        Returns:
            Dict: Export metadata
        """
        logging.info(f"Exporting video: {video_path} for account: {account_data['username']}")
        
        # Create account-specific directory
        account_dir = self._get_account_directory(account_data)
        
        # Generate export filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        export_filename = f"{account_data['username']}_{timestamp}.{os.path.splitext(video_path)[1][1:]}"
        export_path = os.path.join(account_dir, export_filename)
        
        # Copy video file
        shutil.copy2(video_path, export_path)
        
        # Prepare export metadata
        export_metadata = {
            'account': {
                'id': account_data['id'],
                'username': account_data['username'],
                'niche': account_data['niche']
            },
            'video': {
                'id': video_data['id'],
                'title': video_data['title'],
                'story_text': video_data['story_text']
            },
            'export': {
                'path': export_path,
                'filename': export_filename,
                'timestamp': time.time(),
                'datetime': datetime.now().isoformat()
            }
        }
        
        # Export metadata if enabled
        if self.metadata_enabled:
            metadata_path = self._export_metadata(export_metadata, account_dir, timestamp)
            export_metadata['export']['metadata_path'] = metadata_path
        
        logging.info(f"Video exported to: {export_path}")
        return export_metadata
    
    def _get_account_directory(self, account_data: Dict[str, Any]) -> str:
        """Get or create account-specific directory
        
        Args:
            account_data: Account data dictionary
            
        Returns:
            str: Path to account directory
        """
        # Create directory based on username
        account_dir = os.path.join(self.output_dir, account_data['username'])
        os.makedirs(account_dir, exist_ok=True)
        return account_dir
    
    def _export_metadata(self, export_metadata: Dict[str, Any], 
                         account_dir: str, timestamp: str) -> str:
        """Export metadata file
        
        Args:
            export_metadata: Export metadata dictionary
            account_dir: Account directory path
            timestamp: Timestamp string
            
        Returns:
            str: Path to metadata file
        """
        if self.metadata_format == 'json':
            return self._export_json_metadata(export_metadata, account_dir, timestamp)
        else:
            logging.warning(f"Unsupported metadata format: {self.metadata_format}, using JSON")
            return self._export_json_metadata(export_metadata, account_dir, timestamp)
    
    def _export_json_metadata(self, export_metadata: Dict[str, Any], 
                             account_dir: str, timestamp: str) -> str:
        """Export metadata as JSON
        
        Args:
            export_metadata: Export metadata dictionary
            account_dir: Account directory path
            timestamp: Timestamp string
            
        Returns:
            str: Path to JSON metadata file
        """
        metadata_filename = f"{export_metadata['account']['username']}_{timestamp}.json"
        metadata_path = os.path.join(account_dir, metadata_filename)
        
        try:
            with open(metadata_path, 'w') as f:
                json.dump(export_metadata, f, indent=4)
            
            logging.info(f"Exported metadata to: {metadata_path}")
            return metadata_path
        except Exception as e:
            logging.error(f"Error exporting metadata: {str(e)}")
            raise
    
    def get_exported_videos(self, account_username: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get list of exported videos
        
        Args:
            account_username: Optional username to filter by account
            
        Returns:
            List of exported video metadata
        """
        exported_videos = []
        
        # Determine which directories to scan
        if account_username:
            account_dirs = [os.path.join(self.output_dir, account_username)]
        else:
            account_dirs = [os.path.join(self.output_dir, d) for d in os.listdir(self.output_dir) 
                           if os.path.isdir(os.path.join(self.output_dir, d))]
        
        # Scan directories for videos and metadata
        for account_dir in account_dirs:
            if not os.path.exists(account_dir):
                continue
                
            for filename in os.listdir(account_dir):
                file_path = os.path.join(account_dir, filename)
                
                # Check if it's a video file
                if os.path.isfile(file_path) and filename.lower().endswith(('.mp4', '.mov', '.avi')):
                    # Look for corresponding metadata
                    metadata_path = os.path.splitext(file_path)[0] + '.json'
                    
                    if os.path.exists(metadata_path):
                        # Load metadata
                        try:
                            with open(metadata_path, 'r') as f:
                                metadata = json.load(f)
                            exported_videos.append(metadata)
                        except Exception as e:
                            logging.error(f"Error loading metadata from {metadata_path}: {str(e)}")
                            # Create basic metadata
                            exported_videos.append({
                                'export': {
                                    'path': file_path,
                                    'filename': filename,
                                    'timestamp': os.path.getmtime(file_path)
                                }
                            })
                    else:
                        # Create basic metadata
                        account_name = os.path.basename(account_dir)
                        exported_videos.append({
                            'account': {
                                'username': account_name
                            },
                            'export': {
                                'path': file_path,
                                'filename': filename,
                                'timestamp': os.path.getmtime(file_path)
                            }
                        })
        
        # Sort by timestamp (newest first)
        exported_videos.sort(key=lambda x: x['export']['timestamp'], reverse=True)
        return exported_videos
