"""
Database Manager for TikTok Automation
Handles all database operations using SQLite
"""

import os
import sqlite3
import logging
from typing import List, Dict, Any, Optional, Tuple

class DatabaseManager:
    """Manages database operations for TikTok automation"""

    def __init__(self, db_path: str):
        """Initialize database manager

        Args:
            db_path: Path to SQLite database file
        """
        self.db_path = db_path
        self.conn = None
        self.cursor = None

    def connect(self):
        """Connect to the SQLite database"""
        try:
            self.conn = sqlite3.connect(self.db_path)
            self.conn.row_factory = sqlite3.Row  # Return rows as dictionaries
            self.cursor = self.conn.cursor()
            logging.info(f"Connected to database at {self.db_path}")
        except sqlite3.Error as e:
            logging.error(f"Database connection error: {str(e)}")
            raise

    def disconnect(self):
        """Close the database connection"""
        if self.conn:
            self.conn.close()
            self.conn = None
            self.cursor = None
            logging.info("Database connection closed")

    def initialize_database(self):
        """Create database tables if they don't exist"""
        try:
            self.connect()

            # Create accounts table
            self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS accounts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT NOT NULL,
                label TEXT,
                niche TEXT NOT NULL,
                video_duration INTEGER NOT NULL,
                max_videos_per_day INTEGER NOT NULL,
                voice_type TEXT NOT NULL,
                status TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            ''')

            # Create videos table
            self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS videos (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                account_id INTEGER NOT NULL,
                title TEXT NOT NULL,
                story_text TEXT NOT NULL,
                audio_path TEXT,
                image_path TEXT,
                video_path TEXT,
                status TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                uploaded_at TIMESTAMP,
                FOREIGN KEY (account_id) REFERENCES accounts (id)
            )
            ''')

            # Create settings table
            self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS settings (
                key TEXT PRIMARY KEY,
                value TEXT NOT NULL,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            ''')

            self.conn.commit()
            logging.info("Database initialized successfully")
        except sqlite3.Error as e:
            logging.error(f"Database initialization error: {str(e)}")
            raise
        finally:
            self.disconnect()

    # Account operations
    def add_account(self, account_data: Dict[str, Any]) -> int:
        """Add a new TikTok account

        Args:
            account_data: Dictionary containing account information

        Returns:
            int: ID of the newly created account
        """
        try:
            self.connect()
            self.cursor.execute('''
            INSERT INTO accounts (username, label, niche, video_duration, max_videos_per_day, voice_type, status)
            VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                account_data['username'],
                account_data.get('label', ''),
                account_data['niche'],
                account_data['video_duration'],
                account_data['max_videos_per_day'],
                account_data['voice_type'],
                account_data.get('status', 'active')
            ))
            self.conn.commit()
            account_id = self.cursor.lastrowid
            logging.info(f"Added new account: {account_data['username']} (ID: {account_id})")
            return account_id
        except sqlite3.Error as e:
            logging.error(f"Error adding account: {str(e)}")
            raise
        finally:
            self.disconnect()

    def get_account(self, account_id: int) -> Optional[Dict[str, Any]]:
        """Get account by ID

        Args:
            account_id: ID of the account to retrieve

        Returns:
            Dict or None: Account data if found, None otherwise
        """
        try:
            self.connect()
            self.cursor.execute("SELECT * FROM accounts WHERE id = ?", (account_id,))
            account = self.cursor.fetchone()
            return dict(account) if account else None
        except sqlite3.Error as e:
            logging.error(f"Error retrieving account: {str(e)}")
            raise
        finally:
            self.disconnect()

    def get_all_accounts(self, active_only: bool = False) -> List[Dict[str, Any]]:
        """Get all accounts

        Args:
            active_only: If True, return only active accounts

        Returns:
            List of account dictionaries
        """
        try:
            self.connect()
            if active_only:
                self.cursor.execute("SELECT * FROM accounts WHERE status = 'active' ORDER BY username")
            else:
                self.cursor.execute("SELECT * FROM accounts ORDER BY username")
            accounts = [dict(row) for row in self.cursor.fetchall()]
            return accounts
        except sqlite3.Error as e:
            logging.error(f"Error retrieving accounts: {str(e)}")
            raise
        finally:
            self.disconnect()

    def update_account(self, account_id: int, account_data: Dict[str, Any]) -> bool:
        """Update an existing account

        Args:
            account_id: ID of the account to update
            account_data: Dictionary containing updated account information

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            self.connect()
            self.cursor.execute('''
            UPDATE accounts SET
                username = ?,
                label = ?,
                niche = ?,
                video_duration = ?,
                max_videos_per_day = ?,
                voice_type = ?,
                status = ?,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
            ''', (
                account_data['username'],
                account_data.get('label', ''),
                account_data['niche'],
                account_data['video_duration'],
                account_data['max_videos_per_day'],
                account_data['voice_type'],
                account_data.get('status', 'active'),
                account_id
            ))
            self.conn.commit()
            success = self.cursor.rowcount > 0
            if success:
                logging.info(f"Updated account ID {account_id}")
            return success
        except sqlite3.Error as e:
            logging.error(f"Error updating account: {str(e)}")
            raise
        finally:
            self.disconnect()

    def delete_account(self, account_id: int) -> bool:
        """Delete an account

        Args:
            account_id: ID of the account to delete

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            self.connect()
            self.cursor.execute("DELETE FROM accounts WHERE id = ?", (account_id,))
            self.conn.commit()
            success = self.cursor.rowcount > 0
            if success:
                logging.info(f"Deleted account ID {account_id}")
            return success
        except sqlite3.Error as e:
            logging.error(f"Error deleting account: {str(e)}")
            raise
        finally:
            self.disconnect()

    # Video operations
    def add_video(self, video_data: Dict[str, Any]) -> int:
        """Add a new video

        Args:
            video_data: Dictionary containing video information

        Returns:
            int: ID of the newly created video
        """
        try:
            self.connect()
            self.cursor.execute('''
            INSERT INTO videos (account_id, title, story_text, audio_path, image_path, video_path, status)
            VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                video_data['account_id'],
                video_data['title'],
                video_data['story_text'],
                video_data.get('audio_path', None),
                video_data.get('image_path', None),
                video_data.get('video_path', None),
                video_data.get('status', 'pending')
            ))
            self.conn.commit()
            video_id = self.cursor.lastrowid
            logging.info(f"Added new video: {video_data['title']} (ID: {video_id})")
            return video_id
        except sqlite3.Error as e:
            logging.error(f"Error adding video: {str(e)}")
            raise
        finally:
            self.disconnect()

    def get_video(self, video_id: int) -> Optional[Dict[str, Any]]:
        """Get video by ID

        Args:
            video_id: ID of the video to retrieve

        Returns:
            Dict or None: Video data if found, None otherwise
        """
        try:
            self.connect()
            self.cursor.execute("SELECT * FROM videos WHERE id = ?", (video_id,))
            video = self.cursor.fetchone()
            return dict(video) if video else None
        except sqlite3.Error as e:
            logging.error(f"Error retrieving video: {str(e)}")
            raise
        finally:
            self.disconnect()

    def get_account_videos(self, account_id: int) -> List[Dict[str, Any]]:
        """Get all videos for an account

        Args:
            account_id: ID of the account

        Returns:
            List of video dictionaries
        """
        try:
            self.connect()
            self.cursor.execute("SELECT * FROM videos WHERE account_id = ? ORDER BY created_at DESC", (account_id,))
            videos = [dict(row) for row in self.cursor.fetchall()]
            return videos
        except sqlite3.Error as e:
            logging.error(f"Error retrieving account videos: {str(e)}")
            raise
        finally:
            self.disconnect()

    def update_video_status(self, video_id: int, status: str, video_path: Optional[str] = None) -> bool:
        """Update video status and path

        Args:
            video_id: ID of the video to update
            status: New status ('pending', 'generated', 'uploaded', 'failed')
            video_path: Path to the generated video file (optional)

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            self.connect()
            if video_path:
                self.cursor.execute('''
                UPDATE videos SET
                    status = ?,
                    video_path = ?
                WHERE id = ?
                ''', (status, video_path, video_id))
            else:
                self.cursor.execute('''
                UPDATE videos SET status = ? WHERE id = ?
                ''', (status, video_id))

            self.conn.commit()
            success = self.cursor.rowcount > 0
            if success:
                logging.info(f"Updated video ID {video_id} status to {status}")
            return success
        except sqlite3.Error as e:
            logging.error(f"Error updating video status: {str(e)}")
            raise
        finally:
            self.disconnect()

    def mark_video_uploaded(self, video_id: int) -> bool:
        """Mark a video as uploaded

        Args:
            video_id: ID of the video

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            self.connect()
            self.cursor.execute('''
            UPDATE videos SET
                status = 'uploaded',
                uploaded_at = CURRENT_TIMESTAMP
            WHERE id = ?
            ''', (video_id,))
            self.conn.commit()
            success = self.cursor.rowcount > 0
            if success:
                logging.info(f"Marked video ID {video_id} as uploaded")
            return success
        except sqlite3.Error as e:
            logging.error(f"Error marking video as uploaded: {str(e)}")
            raise
        finally:
            self.disconnect()

    # Settings operations
    def set_setting(self, key: str, value: str) -> bool:
        """Set a setting value

        Args:
            key: Setting key
            value: Setting value

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            self.connect()
            self.cursor.execute('''
            INSERT OR REPLACE INTO settings (key, value, updated_at)
            VALUES (?, ?, CURRENT_TIMESTAMP)
            ''', (key, value))
            self.conn.commit()
            success = self.cursor.rowcount > 0
            return success
        except sqlite3.Error as e:
            logging.error(f"Error setting setting: {str(e)}")
            raise
        finally:
            self.disconnect()

    def get_setting(self, key: str, default: str = None) -> Optional[str]:
        """Get a setting value

        Args:
            key: Setting key
            default: Default value if setting not found

        Returns:
            str or None: Setting value if found, default otherwise
        """
        try:
            self.connect()
            self.cursor.execute("SELECT value FROM settings WHERE key = ?", (key,))
            setting = self.cursor.fetchone()
            return dict(setting)['value'] if setting else default
        except sqlite3.Error as e:
            logging.error(f"Error getting setting: {str(e)}")
            return default
        finally:
            self.disconnect()
